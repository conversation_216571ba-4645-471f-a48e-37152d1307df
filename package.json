{"name": "file-manager-chat-app", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "lint": "eslint .", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"expo": "latest", "expo-status-bar": "latest", "expo-constants": "latest", "expo-file-system": "latest", "expo-document-picker": "latest", "expo-sharing": "latest", "expo-haptics": "latest", "@react-navigation/native": "latest", "@react-navigation/stack": "latest", "@react-navigation/bottom-tabs": "latest", "react": "latest", "react-native": "latest", "react-native-gesture-handler": "latest", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "latest", "react-native-screens": "latest", "zustand": "latest", "@react-native-async-storage/async-storage": "latest", "expo-sqlite": "latest", "@langchain/openai": "latest", "@langchain/core": "latest", "langchain": "latest", "expo-secure-store": "latest"}, "devDependencies": {"@babel/core": "latest", "@types/react": "latest", "@types/react-native": "latest", "@testing-library/react-native": "latest", "@testing-library/jest-native": "latest", "eslint": "latest", "eslint-config-expo": "latest", "eslint-config-prettier": "latest", "eslint-plugin-prettier": "latest", "jest": "latest", "jest-expo": "latest", "prettier": "latest", "typescript": "latest"}, "private": true, "engines": {"node": ">=18"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}