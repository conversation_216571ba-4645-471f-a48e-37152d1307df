{"name": "file-manager-chat-app", "version": "1.0.0", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "lint": "eslint .", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"expo": "~53.0.0", "expo-status-bar": "~2.0.0", "expo-constants": "~17.0.0", "expo-file-system": "~18.0.0", "expo-document-picker": "~13.0.3", "expo-sharing": "~13.0.1", "expo-haptics": "~14.0.1", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "zustand": "^4.4.7", "@react-native-async-storage/async-storage": "1.23.1", "expo-sqlite": "~15.1.4", "@langchain/openai": "^0.3.12", "@langchain/core": "^0.3.21", "langchain": "^0.3.7", "expo-secure-store": "~14.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "^18.2.0", "@types/react-native": "^0.73.0", "@testing-library/react-native": "^12.4.2", "@testing-library/jest-native": "^5.4.3", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "jest-expo": "~53.0.0", "prettier": "^3.2.5", "typescript": "^5.3.0"}, "private": true, "engines": {"node": ">=18"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}