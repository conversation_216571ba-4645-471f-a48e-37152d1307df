import React from 'react';
import { Text as RNText, TextStyle, TextProps as RNTextProps } from 'react-native';
import { useTheme } from '../theme';

interface TextProps extends RNTextProps {
  variant?: 'heading' | 'body' | 'caption';
  color?: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'success' | 'warning' | 'error';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  align?: 'left' | 'center' | 'right';
}

export const Text: React.FC<TextProps> = ({
  children,
  variant = 'body',
  color = 'text',
  weight = 'normal',
  align = 'left',
  style,
  ...props
}) => {
  const { theme } = useTheme();

  const getTextStyle = (): TextStyle => {
    const variantStyles = {
      heading: theme.typography.heading,
      body: theme.typography.body,
      caption: theme.typography.caption,
    };

    const colorStyles = {
      primary: theme.colors.primary,
      secondary: theme.colors.secondary,
      text: theme.colors.text,
      textSecondary: theme.colors.textSecondary,
      success: theme.colors.success,
      warning: theme.colors.warning,
      error: theme.colors.error,
    };

    const weightStyles = {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    };

    return {
      ...variantStyles[variant],
      color: colorStyles[color],
      fontWeight: weightStyles[weight],
      textAlign: align,
    };
  };

  return (
    <RNText style={[getTextStyle(), style]} {...props}>
      {children}
    </RNText>
  );
};