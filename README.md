# File Manager Chat App

A React Native application that combines comprehensive file management capabilities with an integrated chat interface, built with TypeScript and modern UI/UX principles.

## Features

- 📁 Comprehensive file management with browse, search, and organize capabilities
- 💬 Integrated chat interface for future AI automation features
- 🎨 Modern UI with shadcn-inspired components and smooth animations
- 📱 Cross-platform support (iOS and Android)
- ⚡ High performance with optimized rendering and caching

## Tech Stack

- **Framework**: Expo SDK 53 + React Native 0.76.3
- **Language**: TypeScript
- **Navigation**: React Navigation 6
- **State Management**: Zustand
- **Animations**: React Native Reanimated 3
- **File System**: Expo File System
- **Storage**: AsyncStorage, Expo SQLite
- **AI**: LangChain + Azure OpenAI

## Getting Started

### Prerequisites

- Node.js >= 18
- Expo CLI (`npm install -g @expo/cli`)
- Expo Go app on your mobile device (for testing)
- Azure OpenAI Service account and API key

### Installation

1. Install Expo CLI globally:
```bash
npm install -g @expo/cli
```

2. Install project dependencies:
```bash
npm install
```

3. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Add your Azure OpenAI credentials:
   ```
   AZURE_OPENAI_API_KEY=your_api_key_here
   AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com/
   AZURE_OPENAI_API_VERSION=2024-12-01-preview
   AZURE_OPENAI_MODEL=gpt-4
   AZURE_MAX_TOKENS=4000
   AZURE_TEMPERATURE=0.1
   ```

4. Start the development server:
```bash
npm start
```

5. Run on your preferred platform:
```bash
# iOS Simulator
npm run ios

# Android Emulator
npm run android

# Web browser
npm run web

# Or scan QR code with Expo Go app
```

## Project Structure

```
src/
├── components/     # Reusable UI components
├── screens/        # Screen components
├── services/       # Business logic and API services
├── stores/         # Zustand state management
├── types/          # TypeScript type definitions
├── utils/          # Utility functions
└── theme/          # Theme configuration and design tokens
```

## Features Implemented

### 📁 File Management
- **Browse & Navigate**: Navigate through device file system with smooth animations
- **File Operations**: Create folders, delete files, copy/move operations
- **Multiple View Modes**: List, grid, and detailed view modes
- **File Type Detection**: Automatic file type recognition with appropriate icons
- **Search Functionality**: Real-time file search with indexing and history
- **Selection Management**: Multi-select files with context actions

### 💬 AI-Powered Chat Interface
- **Azure OpenAI Integration**: Powered by GPT-4 via Azure OpenAI Service
- **LangChain Framework**: Advanced conversation management and context handling
- **Intelligent Responses**: Context-aware file management assistance
- **Message History**: Persistent chat history with conversation context
- **Quick Actions**: Pre-defined quick action buttons for common tasks
- **File References**: Link files to chat messages for context
- **Slide-up Panel**: Smooth animated chat interface

### 🎨 Modern UI/UX
- **Theme System**: Light/dark mode support with smooth transitions
- **Animations**: Comprehensive animation system using Reanimated 3
- **Microinteractions**: Tactile feedback and visual responses
- **Responsive Design**: Adapts to different screen sizes
- **shadcn-inspired**: Modern component design language

### ⚡ Performance
- **Virtualized Lists**: Efficient rendering for large file lists
- **Search Indexing**: Fast file search with background indexing
- **Lazy Loading**: Optimized loading for better performance
- **State Management**: Efficient state management with Zustand

## Architecture

### Services Layer
- **FileSystemService**: Core file operations (CRUD, navigation)
- **FileTypeService**: File type detection and formatting utilities
- **SearchService**: File search, indexing, and history management
- **ChatService**: Chat functionality with message persistence
- **LangChainService**: Azure OpenAI integration with conversation management

### UI Components
- **Animated Components**: Reusable animated UI elements
- **Core Components**: Theme-aware text, buttons, inputs, modals
- **File Components**: Specialized file list and management components
- **Chat Components**: Message bubbles, input, and history components

### State Management
- **Zustand Store**: Centralized app state management
- **Theme Provider**: Theme context and switching logic
- **Persistent Storage**: AsyncStorage for preferences and chat history

## Development

- `npm run typecheck` - Run TypeScript type checking
- `npm run lint` - Run ESLint
- `npm test` - Run Jest tests
- `npm start` - Start Metro bundler

## Usage

1. **File Management**: 
   - Tap folders to navigate
   - Long-press files for context menu
   - Use search icon to find files
   - Toggle view modes with the view button
   - Create folders with the floating action button

2. **Chat Assistant**:
   - Tap the chat button to open the assistant
   - Ask questions about file management
   - Use quick action buttons for common tasks
   - Chat history is automatically saved

3. **AI Chat Assistant**:
   - Ask questions about file management
   - Get contextual help based on your current files
   - Receive intelligent suggestions for organization
   - Context-aware responses using conversation history

4. **Customization**:
   - Theme automatically follows system preference
   - View modes: list, grid, detailed
   - Sort options: name, date, size, type
   - Show/hide hidden files

## AI Features

The app includes a sophisticated AI assistant powered by Azure OpenAI and LangChain:

### Capabilities
- **File Management Expertise**: Specialized knowledge in file organization, search, and management
- **Context Awareness**: Remembers conversation history for better assistance
- **Intelligent Responses**: Provides specific, actionable advice based on your questions
- **File Operation Guidance**: Step-by-step instructions for complex file operations

### Example Interactions
- "How should I organize my photos?"
- "Help me find files from last week"
- "What's the best way to clean up duplicate files?"
- "How can I better organize my documents?"

### Technical Implementation
- **Azure OpenAI**: GPT-4 model for natural language understanding
- **LangChain**: Advanced prompt engineering and conversation management
- **Context Management**: Maintains conversation history for coherent interactions
- **Fallback System**: Graceful degradation if AI service is unavailable

## License

Private project - All rights reserved.