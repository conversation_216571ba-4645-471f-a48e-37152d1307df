import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { FileListItemProps } from '../types';
import { useTheme } from '../theme';
import { FileTypeService } from '../services/FileTypeService';
import { Text } from './Text';
import { Icon, IconNames } from './Icon';
import { ScaleOnPress } from './ScaleOnPress';
import { FadeInView } from './FadeInView';

export const FileListItem: React.FC<FileListItemProps> = ({
  item,
  viewMode,
  onPress,
  onLongPress,
  isSelected,
}) => {
  const { theme } = useTheme();
  const fileTypeService = FileTypeService.getInstance();

  const getFileIcon = () => {
    if (item.type === 'directory') {
      return IconNames.FOLDER;
    }
    return fileTypeService.getFileIcon(item.name);
  };

  const getFileColor = () => {
    if (item.type === 'directory') {
      return theme.colors.primary;
    }
    return fileTypeService.getFileColor(item.name);
  };

  const renderListView = () => (
    <View style={[
      styles.listContainer,
      {
        backgroundColor: isSelected ? theme.colors.primary + '20' : 'transparent',
        borderColor: isSelected ? theme.colors.primary : 'transparent',
      }
    ]}>
      <View style={styles.listContent}>
        <Icon
          name={getFileIcon()}
          size={24}
          color={getFileColor()}
          style={styles.icon}
        />
        <View style={styles.fileInfo}>
          <Text
            variant="body"
            color="text"
            weight="medium"
            numberOfLines={1}
            style={styles.fileName}>
            {item.name}
          </Text>
          <View style={styles.metadata}>
            <Text variant="caption" color="textSecondary">
              {fileTypeService.formatDate(item.modifiedDate)}
            </Text>
            {item.type === 'file' && (
              <>
                <Text variant="caption" color="textSecondary"> • </Text>
                <Text variant="caption" color="textSecondary">
                  {fileTypeService.formatFileSize(item.size)}
                </Text>
              </>
            )}
          </View>
        </View>
      </View>
      {isSelected && (
        <Icon
          name="check"
          size={20}
          color={theme.colors.primary}
        />
      )}
    </View>
  );

  const renderGridView = () => (
    <View style={[
      styles.gridContainer,
      {
        backgroundColor: theme.colors.card,
        borderColor: isSelected ? theme.colors.primary : theme.colors.border,
        borderWidth: isSelected ? 2 : 1,
      }
    ]}>
      <View style={styles.gridIconContainer}>
        <Icon
          name={getFileIcon()}
          size={32}
          color={getFileColor()}
        />
        {isSelected && (
          <View style={[
            styles.selectionBadge,
            { backgroundColor: theme.colors.primary }
          ]}>
            <Icon
              name="check"
              size={12}
              color="#FFFFFF"
            />
          </View>
        )}
      </View>
      <Text
        variant="caption"
        color="text"
        weight="medium"
        numberOfLines={2}
        style={styles.gridFileName}>
        {item.name}
      </Text>
      {item.type === 'file' && (
        <Text variant="caption" color="textSecondary" numberOfLines={1}>
          {fileTypeService.formatFileSize(item.size)}
        </Text>
      )}
    </View>
  );

  const renderDetailedView = () => (
    <View style={[
      styles.detailedContainer,
      {
        backgroundColor: isSelected ? theme.colors.primary + '20' : 'transparent',
        borderColor: isSelected ? theme.colors.primary : 'transparent',
      }
    ]}>
      <View style={styles.detailedContent}>
        <Icon
          name={getFileIcon()}
          size={28}
          color={getFileColor()}
          style={styles.icon}
        />
        <View style={styles.detailedInfo}>
          <Text
            variant="body"
            color="text"
            weight="medium"
            numberOfLines={1}
            style={styles.fileName}>
            {item.name}
          </Text>
          <View style={styles.detailedMetadata}>
            <View style={styles.metadataRow}>
              <Text variant="caption" color="textSecondary">
                Modified: {fileTypeService.formatDate(item.modifiedDate)}
              </Text>
            </View>
            <View style={styles.metadataRow}>
              <Text variant="caption" color="textSecondary">
                Type: {item.type === 'directory' ? 'Folder' : fileTypeService.getFileCategory(item.name)}
              </Text>
              {item.type === 'file' && (
                <>
                  <Text variant="caption" color="textSecondary"> • </Text>
                  <Text variant="caption" color="textSecondary">
                    Size: {fileTypeService.formatFileSize(item.size)}
                  </Text>
                </>
              )}
            </View>
          </View>
        </View>
      </View>
      {isSelected && (
        <Icon
          name="check"
          size={20}
          color={theme.colors.primary}
        />
      )}
    </View>
  );

  const renderContent = () => {
    switch (viewMode) {
      case 'grid':
        return renderGridView();
      case 'detailed':
        return renderDetailedView();
      default:
        return renderListView();
    }
  };

  return (
    <FadeInView
      direction="up"
      distance={10}
      duration={200}
      style={viewMode === 'grid' ? styles.gridWrapper : undefined}>
      <ScaleOnPress
        onPress={onPress}
        onLongPress={onLongPress}
        scaleValue={0.98}>
        {renderContent()}
      </ScaleOnPress>
    </FadeInView>
  );
};

const styles = StyleSheet.create({
  // List view styles
  listContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 2,
  },
  listContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // Grid view styles
  gridWrapper: {
    flex: 1,
    margin: 4,
  },
  gridContainer: {
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'space-between',
  },
  gridIconContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  selectionBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridFileName: {
    textAlign: 'center',
    marginBottom: 4,
  },
  
  // Detailed view styles
  detailedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 2,
  },
  detailedContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailedInfo: {
    flex: 1,
  },
  detailedMetadata: {
    marginTop: 4,
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  
  // Common styles
  icon: {
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    marginBottom: 2,
  },
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});