import { create } from 'zustand';
import { AppState } from '../types';
import * as FileSystem from 'expo-file-system';

interface AppStore extends AppState {
  // Actions
  setCurrentPath: (path: string) => void;
  setSelectedFiles: (files: string[]) => void;
  toggleFileSelection: (filePath: string) => void;
  clearSelection: () => void;
  setViewMode: (mode: 'list' | 'grid' | 'detailed') => void;
  setSortBy: (sortBy: 'name' | 'date' | 'size' | 'type') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  toggleSortOrder: () => void;
  setShowHiddenFiles: (show: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  setChatVisible: (visible: boolean) => void;
  toggleChat: () => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // Initial state
  currentPath: FileSystem.documentDirectory || '',
  selectedFiles: [],
  viewMode: 'list',
  sortBy: 'name',
  sortOrder: 'asc',
  showHiddenFiles: false,
  theme: 'light',
  chatVisible: false,

  // Actions
  setCurrentPath: (path) => set({ currentPath: path }),
  
  setSelectedFiles: (files) => set({ selectedFiles: files }),
  
  toggleFileSelection: (filePath) => set((state) => ({
    selectedFiles: state.selectedFiles.includes(filePath)
      ? state.selectedFiles.filter(path => path !== filePath)
      : [...state.selectedFiles, filePath]
  })),
  
  clearSelection: () => set({ selectedFiles: [] }),
  
  setViewMode: (mode) => set({ viewMode: mode }),
  
  setSortBy: (sortBy) => set({ sortBy }),
  
  setSortOrder: (order) => set({ sortOrder: order }),
  
  toggleSortOrder: () => set((state) => ({
    sortOrder: state.sortOrder === 'asc' ? 'desc' : 'asc'
  })),
  
  setShowHiddenFiles: (show) => set({ showHiddenFiles: show }),
  
  setTheme: (theme) => set({ theme }),
  
  toggleTheme: () => set((state) => ({
    theme: state.theme === 'light' ? 'dark' : 'light'
  })),
  
  setChatVisible: (visible) => set({ chatVisible: visible }),
  
  toggleChat: () => set((state) => ({ chatVisible: !state.chatVisible })),
}));