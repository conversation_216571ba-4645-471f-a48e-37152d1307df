import React, { useEffect } from 'react';
import {
  Modal as RNModal,
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Dimensions,
  ViewStyle,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useTheme } from '../theme';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  animationType?: 'fade' | 'slide' | 'scale';
  backdropOpacity?: number;
  style?: ViewStyle;
  closeOnBackdropPress?: boolean;
}

const { height: screenHeight } = Dimensions.get('window');

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  children,
  animationType = 'fade',
  backdropOpacity = 0.5,
  style,
  closeOnBackdropPress = true,
}) => {
  const { theme } = useTheme();
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const translateY = useSharedValue(screenHeight);

  useEffect(() => {
    if (visible) {
      opacity.value = withTiming(1, { duration: 300 });
      
      if (animationType === 'scale') {
        scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      } else if (animationType === 'slide') {
        translateY.value = withSpring(0, { damping: 15, stiffness: 300 });
      }
    } else {
      opacity.value = withTiming(0, { duration: 200 });
      
      if (animationType === 'scale') {
        scale.value = withTiming(0.8, { duration: 200 });
      } else if (animationType === 'slide') {
        translateY.value = withTiming(screenHeight, { duration: 200 });
      }
    }
  }, [visible, animationType, opacity, scale, translateY]);

  const backdropStyle = useAnimatedStyle(() => ({
    opacity: opacity.value * backdropOpacity,
  }));

  const contentStyle = useAnimatedStyle(() => {
    const baseStyle = {
      opacity: opacity.value,
    };

    if (animationType === 'scale') {
      return {
        ...baseStyle,
        transform: [{ scale: scale.value }],
      };
    } else if (animationType === 'slide') {
      return {
        ...baseStyle,
        transform: [{ translateY: translateY.value }],
      };
    }

    return baseStyle;
  });

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent>
      <View style={styles.container}>
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View style={[styles.backdrop, backdropStyle]} />
        </TouchableWithoutFeedback>
        
        <Animated.View
          style={[
            styles.content,
            {
              backgroundColor: theme.colors.background,
              borderRadius: theme.borderRadius.lg,
            },
            contentStyle,
            style,
          ]}>
          {children}
        </Animated.View>
      </View>
    </RNModal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
  },
  content: {
    maxWidth: '90%',
    maxHeight: '80%',
    padding: 20,
  },
});