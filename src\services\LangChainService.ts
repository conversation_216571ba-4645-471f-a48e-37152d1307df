import { ChatMessage } from '../types';
import { AzureChatOpenAI } from '@langchain/openai';
import { HumanMessage, SystemMessage, AIMessage } from '@langchain/core/messages';

// Environment variables interface
interface AzureConfig {
  apiKey: string;
  endpoint: string;
  apiVersion: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export class LangChainService {
  private static instance: LangChainService;
  private config: AzureConfig;
  private azureOpenAI: AzureChatOpenAI;

  public static getInstance(): LangChainService {
    if (!LangChainService.instance) {
      LangChainService.instance = new LangChainService();
    }
    return LangChainService.instance;
  }

  private constructor() {
    // Use hardcoded values for now since we removed babel config
    this.config = {
      apiKey: '84a0tBbHjYxc2hjaVeJehwaQckrlu1BwWf2Lvf77lYTghrWQvAXQJQQJ99BGAC77bzfXJ3w3AAABACOGeuWT',
      endpoint: 'https://sivanithish-test.openai.azure.com/',
      apiVersion: '2024-12-01-preview',
      model: 'gpt-4',
      maxTokens: 4000,
      temperature: 0.1,
    };
    
    this.initializeAzureOpenAI();
  }

  private initializeAzureOpenAI() {
    this.azureOpenAI = new AzureChatOpenAI({
      azureOpenAIApiKey: this.config.apiKey,
      azureOpenAIApiInstanceName: 'sivanithish-test',
      azureOpenAIApiDeploymentName: this.config.model,
      azureOpenAIApiVersion: this.config.apiVersion,
      maxTokens: this.config.maxTokens,
      temperature: this.config.temperature,
    });
  }

  /**
   * Generate AI response using Azure OpenAI
   */
  async generateResponse(message: string, conversationHistory: ChatMessage[] = []): Promise<string> {
    try {
      const systemPrompt = this.getSystemPrompt();
      const messages = this.buildLangChainMessages(systemPrompt, message, conversationHistory);

      const response = await this.azureOpenAI.invoke(messages);
      return response.content as string;
    } catch (error) {
      console.error('LangChain service error:', error);
      return this.getFallbackResponse(message);
    }
  }

  /**
   * Generate contextual response for file operations
   */
  async generateFileActionResponse(filePath: string, action: string, context?: string): Promise<string> {
    try {
      const fileName = filePath.split('/').pop() || 'file';
      const prompt = `User performed "${action}" on file "${fileName}". ${context || ''} Provide a helpful response about this file operation.`;
      
      const response = await this.generateResponse(prompt);
      return response;
    } catch (error) {
      console.error('File action response error:', error);
      return this.getFileActionFallback(filePath, action);
    }
  }

  private getSystemPrompt(): string {
    return `You are a helpful AI assistant specialized in file management and organization. You help users with:

1. **File Organization**: Suggest folder structures, naming conventions, and organization strategies
2. **File Search**: Help users find files using various criteria and search techniques  
3. **File Operations**: Guide users through copying, moving, deleting, and renaming files
4. **File Types**: Explain different file formats and their uses
5. **Storage Management**: Advise on storage optimization and cleanup strategies
6. **Productivity**: Recommend workflows and best practices for file management

Key Guidelines:
- Be concise and practical in your responses
- Provide step-by-step instructions when needed
- Suggest specific actions the user can take in the file manager
- Be helpful and encouraging
- Focus on file management topics

Current context: You are integrated into a React Native file manager app where users can browse, search, and manage their files.`;
  }

  private buildLangChainMessages(systemPrompt: string, currentMessage: string, history: ChatMessage[]): (SystemMessage | HumanMessage | AIMessage)[] {
    const messages: (SystemMessage | HumanMessage | AIMessage)[] = [
      new SystemMessage(systemPrompt)
    ];

    // Add recent conversation history (last 10 messages)
    const recentHistory = history.slice(-10);
    for (const msg of recentHistory) {
      if (msg.sender === 'user') {
        messages.push(new HumanMessage(msg.text));
      } else {
        messages.push(new AIMessage(msg.text));
      }
    }

    // Add current message
    messages.push(new HumanMessage(currentMessage));

    return messages;
  }



  private getFallbackResponse(message: string): string {
    const lowercaseMessage = message.toLowerCase();
    
    // File management related responses
    if (lowercaseMessage.includes('search') || lowercaseMessage.includes('find')) {
      return 'I can help you search for files! Use the search icon in the file manager to find files by name, type, or content. You can also filter results by date, size, or file type.';
    }
    
    if (lowercaseMessage.includes('organize') || lowercaseMessage.includes('sort')) {
      return 'Great! I can help you organize your files. You can sort files by name, date, size, or type. You can also create folders to group related files together.';
    }
    
    if (lowercaseMessage.includes('delete') || lowercaseMessage.includes('remove')) {
      return 'To delete files, long-press on any file or folder to open the context menu, then select delete. Be careful as this action cannot be undone!';
    }
    
    if (lowercaseMessage.includes('copy') || lowercaseMessage.includes('move')) {
      return 'You can copy or move files by long-pressing on them to open the context menu. Select copy or move, then navigate to your destination folder and paste.';
    }
    
    if (lowercaseMessage.includes('hello') || lowercaseMessage.includes('hi')) {
      return 'Hello! I\'m here to help you manage your files efficiently. What would you like to do today?';
    }
    
    if (lowercaseMessage.includes('help')) {
      return 'I can help you with:\n• Searching and finding files\n• Organizing and sorting files\n• Creating, copying, and moving files\n• Understanding file types and sizes\n• Managing folders and directories\n\nWhat specific task would you like help with?';
    }
    
    // Default responses
    const defaultResponses = [
      'That\'s interesting! How can I help you with your file management needs?',
      'I understand. Is there anything specific you\'d like to do with your files?',
      'Thanks for sharing! Let me know if you need help organizing or finding any files.',
      'I\'m here to help with your file management tasks. What would you like to do?',
      'That sounds good! Feel free to ask me about searching, organizing, or managing your files.',
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }

  private getFileActionFallback(filePath: string, action: string): string {
    const fileName = filePath.split('/').pop() || 'file';
    
    switch (action.toLowerCase()) {
      case 'open':
        return `Opening ${fileName}. If it's a supported file type, you should see a preview.`;
      case 'copy':
        return `${fileName} has been copied. Navigate to your destination folder and paste it there.`;
      case 'move':
        return `${fileName} is ready to be moved. Select your destination folder to complete the move.`;
      case 'delete':
        return `${fileName} has been deleted. This action cannot be undone.`;
      case 'rename':
        return `${fileName} has been renamed successfully.`;
      case 'share':
        return `Sharing ${fileName}. Choose how you'd like to share this file.`;
      default:
        return `Action "${action}" performed on ${fileName}.`;
    }
  }
}