import React from 'react';
import { View, Text, ViewStyle } from 'react-native';
import { useTheme } from '../theme';

// Simple icon component using emoji/text
// In a real app, you'd use react-native-vector-icons or similar
interface IconProps {
  name: string;
  size?: number;
  color?: string;
  style?: ViewStyle;
}

export const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color,
  style,
}) => {
  const { theme } = useTheme();
  const iconColor = color || theme.colors.text;

  // Map icon names to emoji/symbols
  const iconMap: Record<string, string> = {
    folder: '📁',
    file: '📄',
    search: '🔍',
    chat: '💬',
    settings: '⚙️',
    back: '←',
    more: '⋯',
    delete: '🗑️',
    copy: '📋',
    move: '📦',
    rename: '✏️',
    create: '+',
    image: '🖼️',
    text: '📝',
    audio: '🎵',
    video: '🎬',
    archive: '📦',
    check: '✓',
    close: '✕',
    send: '→',
    clear: '🗑️',
    view: '👁️',
  };

  const iconSymbol = iconMap[name] || '?';

  return (
    <View
      style={[
        {
          width: size,
          height: size,
          alignItems: 'center',
          justifyContent: 'center',
        },
        style,
      ]}>
      <Text
        style={{
          fontSize: size * 0.8,
          color: iconColor,
          textAlign: 'center',
        }}>
        {iconSymbol}
      </Text>
    </View>
  );
};

// Common icon names for the app
export const IconNames = {
  FOLDER: 'folder',
  FILE: 'file',
  SEARCH: 'search',
  CHAT: 'chat',
  SETTINGS: 'settings',
  BACK: 'back',
  MORE: 'more',
  DELETE: 'delete',
  COPY: 'copy',
  MOVE: 'move',
  RENAME: 'rename',
  CREATE: 'create',
  IMAGE: 'image',
  TEXT: 'text',
  AUDIO: 'audio',
  VIDEO: 'video',
  ARCHIVE: 'archive',
} as const;