import React, { useState } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider, useTheme } from './theme';
import { FileManagerScreen } from './screens/FileManagerScreen';
import { ChatScreen } from './screens/ChatScreen';
import { SlideUpPanel } from './components/SlideUpPanel';
import { ScaleOnPress } from './components/ScaleOnPress';
import { Icon, IconNames } from './components/Icon';
import { FileItem, ChatMessage } from './types';
import { ChatService } from './services/ChatService';

const MainApp: React.FC = () => {
  const { theme, isDark } = useTheme();
  const [chatVisible, setChatVisible] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  
  const chatService = ChatService.getInstance();

  const handleFileSelect = (file: FileItem) => {
    // Handle file selection - could open preview, etc.
    console.log('File selected:', file.name);
  };

  const handleSendMessage = async (messageText: string) => {
    try {
      const response = await chatService.sendMessage(messageText);
      // Reload messages from service
      const updatedMessages = await chatService.getMessageHistory();
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const loadMessages = async () => {
    try {
      const history = await chatService.getMessageHistory();
      setMessages(history);
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  React.useEffect(() => {
    if (chatVisible) {
      loadMessages();
    }
  }, [chatVisible]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      <FileManagerScreen
        onFileSelect={handleFileSelect}
      />

      {/* Floating Chat Button */}
      <ScaleOnPress onPress={() => setChatVisible(true)}>
        <View style={[styles.chatFab, { backgroundColor: theme.colors.primary }]}>
          <Icon name={IconNames.CHAT} size={24} color="#FFFFFF" />
        </View>
      </ScaleOnPress>

      {/* Chat Panel */}
      <SlideUpPanel
        visible={chatVisible}
        onClose={() => setChatVisible(false)}
        height="80%">
        <ChatScreen
          isVisible={chatVisible}
          onClose={() => setChatVisible(false)}
          messages={messages}
          onSendMessage={handleSendMessage}
        />
      </SlideUpPanel>
    </SafeAreaView>
  );
};

function App(): React.JSX.Element {
  return (
    <ThemeProvider>
      <MainApp />
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  chatFab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default App;