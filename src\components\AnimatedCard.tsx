import React from 'react';
import { View, ViewStyle } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { useTheme, createShadow } from '../theme';

interface AnimatedCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  elevation?: number;
  onPress?: () => void;
  pressable?: boolean;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  style,
  elevation = 2,
  onPress,
  pressable = false,
}) => {
  const { theme, isDark } = useTheme();
  const scale = useSharedValue(1);
  const pressed = useSharedValue(0);

  const handlePressIn = () => {
    if (pressable) {
      scale.value = withSpring(0.98, { damping: 15, stiffness: 300 });
      pressed.value = withSpring(1, { damping: 15, stiffness: 300 });
    }
  };

  const handlePressOut = () => {
    if (pressable) {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      pressed.value = withSpring(0, { damping: 15, stiffness: 300 });
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    const shadowElevation = interpolate(
      pressed.value,
      [0, 1],
      [elevation, elevation + 2]
    );

    return {
      transform: [{ scale: scale.value }],
      ...createShadow(shadowElevation, isDark),
    };
  });

  const cardStyle: ViewStyle = {
    backgroundColor: theme.colors.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  };

  const Component = pressable ? Animated.createAnimatedComponent(View) : Animated.View;

  if (pressable && onPress) {
    return (
      <Component
        style={[animatedStyle, cardStyle, style]}
        onTouchStart={handlePressIn}
        onTouchEnd={handlePressOut}
        onPress={onPress}>
        {children}
      </Component>
    );
  }

  return (
    <Animated.View style={[animatedStyle, cardStyle, style]}>
      {children}
    </Animated.View>
  );
};